/*
 * xmlmodule.c : 动态模块加载的基本API
 */

/* 为了在zOS上定义RTLD_GLOBAL和RTLD_NOW */
#if defined(__MVS__)
#define _UNIX03_SOURCE
#endif

#define IN_LIBXML
#include "libxml.h"

#include <string.h>
#include <libxml/xmlmodule.h>
#include <libxml/xmlmemory.h>
#include <libxml/xmlerror.h>
#include <libxml/xmlstring.h>

#include "private/error.h"

#ifdef LIBXML_MODULES_ENABLED

struct _xmlModule {
    unsigned char *name;
    void *handle;
};

static void *xmlModulePlatformOpen(const char *name);
static int xmlModulePlatformClose(void *handle);
static int xmlModulePlatformSymbol(void *handle, const char *name, void **result);

/************************************************************************
 *									*
 *		模块内存错误处理器				*
 *									*
 ************************************************************************/

/**
 * 根据给定的名称或路径打开一个模块/共享库
 * 注意：由于可移植性问题，只能保证使用ASCII的`name`的行为。
 * 我们不能保证UTF-8字符串能正常工作，这就是为什么name是const char *
 * 而不是const xmlChar *的原因。
 * options参数尚未实现。
 *
 * @param name  模块名称
 * @param options  一组xmlModuleOption选项
 * @returns 模块的句柄，出错时返回NULL
 */
xmlModulePtr
xmlModuleOpen(const char *name, int options ATTRIBUTE_UNUSED)
{
    xmlModulePtr module;

    module = (xmlModulePtr) xmlMalloc(sizeof(xmlModule));
    if (module == NULL)
        return (NULL);

    memset(module, 0, sizeof(xmlModule));

    module->handle = xmlModulePlatformOpen(name);

    if (module->handle == NULL) {
        xmlFree(module);
        return(NULL);
    }

    module->name = xmlStrdup((const xmlChar *) name);
    return (module);
}

/**
 * 在给定模块中查找符号地址
 * 注意：由于可移植性问题，只能保证使用ASCII的`name`的行为。
 * 我们不能保证UTF-8字符串能正常工作，这就是为什么name是const char *
 * 而不是const xmlChar *的原因。
 *
 * @param module  模块
 * @param name  符号的名称
 * @param symbol  结果符号地址
 * @returns 如果找到符号返回0，出错时返回-1
 */
int
xmlModuleSymbol(xmlModulePtr module, const char *name, void **symbol)
{
    int rc = -1;

    if ((NULL == module) || (symbol == NULL) || (name == NULL))
        return rc;

    rc = xmlModulePlatformSymbol(module->handle, name, symbol);

    if (rc == -1)
        return rc;

    return rc;
}

/**
 * 关闭操作卸载关联的模块并释放与模块关联的数据。
 *
 * @param module  模块句柄
 * @returns 成功时返回0，参数错误时返回-1，
 *         如果模块无法关闭/卸载时返回-2。
 */
int
xmlModuleClose(xmlModulePtr module)
{
    int rc;

    if (NULL == module)
        return -1;

    rc = xmlModulePlatformClose(module->handle);

    if (rc != 0)
        return -2;

    rc = xmlModuleFree(module);
    return (rc);
}

/**
 * 释放操作释放与模块关联的数据，但不卸载可能仍在使用的
 * 关联共享库。
 *
 * @param module  模块句柄
 * @returns 成功时返回0，参数错误时返回-1
 */
int
xmlModuleFree(xmlModulePtr module)
{
    if (NULL == module)
        return -1;

    xmlFree(module->name);
    xmlFree(module);

    return (0);
}

#if defined(HAVE_DLOPEN) && !defined(_WIN32)
#include <dlfcn.h>

#ifndef RTLD_GLOBAL            /* 为Tru64 UNIX 4.0 */
#define RTLD_GLOBAL 0
#endif

/**
 * @param name  模块的路径
 * @returns 成功时返回句柄，出错时返回零。
 */

static void *
xmlModulePlatformOpen(const char *name)
{
    return dlopen(name, RTLD_GLOBAL | RTLD_NOW);
}

/*
 * @param handle  模块的句柄
 *
 * @returns 成功时返回0，出错时返回非零值。
 */

static int
xmlModulePlatformClose(void *handle)
{
    return dlclose(handle);
}

/*
 * @returns 成功时返回0并在result中返回加载的符号，出错时返回-1。
 */

static int
xmlModulePlatformSymbol(void *handle, const char *name, void **symbol)
{
    *symbol = dlsym(handle, name);
    if (dlerror() != NULL) {
	return -1;
    }
    return 0;
}

#else /* ! HAVE_DLOPEN */

#ifdef HAVE_SHLLOAD             /* HAVE_SHLLOAD */
#include <dl.h>
/*
 * @returns 成功时返回句柄，出错时返回零。
 */

static void *
xmlModulePlatformOpen(const char *name)
{
    return shl_load(name, BIND_IMMEDIATE, 0L);
}

/*
 * @returns 成功时返回0，出错时返回非零值。
 */

static int
xmlModulePlatformClose(void *handle)
{
    return shl_unload(handle);
}

/*
 * @returns 成功时返回0并在result中返回加载的符号，出错时返回-1。
 */

static int
xmlModulePlatformSymbol(void *handle, const char *name, void **symbol)
{
    int rc;

    errno = 0;
    rc = shl_findsym(&handle, name, TYPE_UNDEFINED, symbol);
    return rc;
}

#endif /* HAVE_SHLLOAD */
#endif /* ! HAVE_DLOPEN */

#if defined(_WIN32)

#define WIN32_LEAN_AND_MEAN
#include <windows.h>

/*
 * @returns 成功时返回句柄，出错时返回零。
 */

static void *
xmlModulePlatformOpen(const char *name)
{
    return LoadLibraryA(name);
}

/*
 * @returns 成功时返回0，出错时返回非零值。
 */

static int
xmlModulePlatformClose(void *handle)
{
    int rc;

    rc = FreeLibrary(handle);
    return (0 == rc);
}

/*
 * @returns 成功时返回0并在result中返回加载的符号，出错时返回-1。
 */

static int
xmlModulePlatformSymbol(void *handle, const char *name, void **symbol)
{
    FARPROC proc = GetProcAddress(handle, name);

    memcpy(symbol, &proc, sizeof(proc));
    return (NULL == *symbol) ? -1 : 0;
}

#endif /* _WIN32 */

#endif /* LIBXML_MODULES_ENABLED */
